package com.example.pdd_nixinag;

import android.app.Activity;
import android.os.Bundle;
import android.widget.TextView;

/**
 * 拼多多网络Hook模块主界面
 * 这是一个Xposed模块，主要功能在XposedMain中实现
 */
public class MainActivity extends Activity {
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        
        // 创建简单的界面显示模块信息
        TextView textView = new TextView(this);
        textView.setText("拼多多网络Hook模块\n\n" +
                "功能说明：\n" +
                "• Hook拼多多App的网络请求\n" +
                "• 捕获HTTP响应数据\n" +
                "• 打印原始响应内容到日志\n\n" +
                "使用方法：\n" +
                "1. 确保已安装Xposed框架\n" +
                "2. 在Xposed管理器中激活此模块\n" +
                "3. 重启设备\n" +
                "4. 打开拼多多App进行操作\n" +
                "5. 查看Xposed日志或logcat输出\n\n" +
                "日志标签：PDD_NetworkHook");
        
        textView.setPadding(50, 50, 50, 50);
        textView.setTextSize(16);
        setContentView(textView);
    }
}
