{"logs": [{"outputFile": "com.example.pdd_nixinag.app-mergeDebugResources-29:/values-my/values-my.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\11986f19adc31a9b38ad6a88093262d6\\transformed\\core-1.13.0\\res\\values-my\\values-my.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,158,262,365,467,572,678,797", "endColumns": "102,103,102,101,104,105,118,100", "endOffsets": "153,257,360,462,567,673,792,893"}, "to": {"startLines": "38,39,40,41,42,43,44,116", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3547,3650,3754,3857,3959,4064,4170,10034", "endColumns": "102,103,102,101,104,105,118,100", "endOffsets": "3645,3749,3852,3954,4059,4165,4284,10130"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\bd0f378145676ccc32210984b212ca64\\transformed\\appcompat-1.7.1\\res\\values-my\\values-my.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,218,325,441,528,637,760,839,917,1008,1101,1196,1290,1390,1483,1578,1672,1763,1854,1939,2054,2163,2262,2388,2495,2603,2763,2866", "endColumns": "112,106,115,86,108,122,78,77,90,92,94,93,99,92,94,93,90,90,84,114,108,98,125,106,107,159,102,85", "endOffsets": "213,320,436,523,632,755,834,912,1003,1096,1191,1285,1385,1478,1573,1667,1758,1849,1934,2049,2158,2257,2383,2490,2598,2758,2861,2947"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,112", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "335,448,555,671,758,867,990,1069,1147,1238,1331,1426,1520,1620,1713,1808,1902,1993,2084,2169,2284,2393,2492,2618,2725,2833,2993,9703", "endColumns": "112,106,115,86,108,122,78,77,90,92,94,93,99,92,94,93,90,90,84,114,108,98,125,106,107,159,102,85", "endOffsets": "443,550,666,753,862,985,1064,1142,1233,1326,1421,1515,1615,1708,1803,1897,1988,2079,2164,2279,2388,2487,2613,2720,2828,2988,3091,9784"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\7146d326271694f6c4b3d2404ca60108\\transformed\\material-1.12.0\\res\\values-my\\values-my.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,285,386,485,561,652,736,842,971,1056,1119,1184,1274,1349,1408,1499,1562,1627,1686,1757,1819,1876,1995,2053,2114,2169,2242,2374,2465,2549,2649,2735,2824,2965,3043,3120,3243,3335,3412,3470,3521,3587,3659,3741,3812,3890,3965,4039,4111,4190,4298,4395,4476,4562,4654,4728,4807,4893,4947,5023,5091,5174,5255,5317,5381,5444,5512,5624,5735,5839,5952,6013,6068,6150,6237,6317", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,100,98,75,90,83,105,128,84,62,64,89,74,58,90,62,64,58,70,61,56,118,57,60,54,72,131,90,83,99,85,88,140,77,76,122,91,76,57,50,65,71,81,70,77,74,73,71,78,107,96,80,85,91,73,78,85,53,75,67,82,80,61,63,62,67,111,110,103,112,60,54,81,86,79,77", "endOffsets": "280,381,480,556,647,731,837,966,1051,1114,1179,1269,1344,1403,1494,1557,1622,1681,1752,1814,1871,1990,2048,2109,2164,2237,2369,2460,2544,2644,2730,2819,2960,3038,3115,3238,3330,3407,3465,3516,3582,3654,3736,3807,3885,3960,4034,4106,4185,4293,4390,4471,4557,4649,4723,4802,4888,4942,5018,5086,5169,5250,5312,5376,5439,5507,5619,5730,5834,5947,6008,6063,6145,6232,6312,6390"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,113,114,115", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3096,3197,3296,3372,3463,4289,4395,4524,4609,4672,4737,4827,4902,4961,5052,5115,5180,5239,5310,5372,5429,5548,5606,5667,5722,5795,5927,6018,6102,6202,6288,6377,6518,6596,6673,6796,6888,6965,7023,7074,7140,7212,7294,7365,7443,7518,7592,7664,7743,7851,7948,8029,8115,8207,8281,8360,8446,8500,8576,8644,8727,8808,8870,8934,8997,9065,9177,9288,9392,9505,9566,9621,9789,9876,9956", "endLines": "5,33,34,35,36,37,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,113,114,115", "endColumns": "12,100,98,75,90,83,105,128,84,62,64,89,74,58,90,62,64,58,70,61,56,118,57,60,54,72,131,90,83,99,85,88,140,77,76,122,91,76,57,50,65,71,81,70,77,74,73,71,78,107,96,80,85,91,73,78,85,53,75,67,82,80,61,63,62,67,111,110,103,112,60,54,81,86,79,77", "endOffsets": "330,3192,3291,3367,3458,3542,4390,4519,4604,4667,4732,4822,4897,4956,5047,5110,5175,5234,5305,5367,5424,5543,5601,5662,5717,5790,5922,6013,6097,6197,6283,6372,6513,6591,6668,6791,6883,6960,7018,7069,7135,7207,7289,7360,7438,7513,7587,7659,7738,7846,7943,8024,8110,8202,8276,8355,8441,8495,8571,8639,8722,8803,8865,8929,8992,9060,9172,9283,9387,9500,9561,9616,9698,9871,9951,10029"}}]}]}