{"logs": [{"outputFile": "com.example.pdd_nixinag.app-mergeDebugResources-29:/values-sq/values-sq.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\7146d326271694f6c4b3d2404ca60108\\transformed\\material-1.12.0\\res\\values-sq\\values-sq.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,267,346,424,510,610,702,803,929,1012,1077,1142,1242,1312,1371,1469,1531,1595,1654,1726,1789,1843,1960,2017,2079,2133,2205,2340,2423,2502,2598,2681,2759,2900,2984,3066,3214,3304,3382,3435,3494,3560,3631,3710,3781,3864,3940,4018,4090,4163,4267,4356,4428,4522,4621,4695,4767,4868,4918,5003,5069,5159,5248,5310,5374,5437,5504,5620,5733,5842,5947,6004,6067,6150,6235,6309", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,78,77,85,99,91,100,125,82,64,64,99,69,58,97,61,63,58,71,62,53,116,56,61,53,71,134,82,78,95,82,77,140,83,81,147,89,77,52,58,65,70,78,70,82,75,77,71,72,103,88,71,93,98,73,71,100,49,84,65,89,88,61,63,62,66,115,112,108,104,56,62,82,84,73,77", "endOffsets": "262,341,419,505,605,697,798,924,1007,1072,1137,1237,1307,1366,1464,1526,1590,1649,1721,1784,1838,1955,2012,2074,2128,2200,2335,2418,2497,2593,2676,2754,2895,2979,3061,3209,3299,3377,3430,3489,3555,3626,3705,3776,3859,3935,4013,4085,4158,4262,4351,4423,4517,4616,4690,4762,4863,4913,4998,5064,5154,5243,5305,5369,5432,5499,5615,5728,5837,5942,5999,6062,6145,6230,6304,6382"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,113,114,115", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3013,3092,3170,3256,3356,4185,4286,4412,4495,4560,4625,4725,4795,4854,4952,5014,5078,5137,5209,5272,5326,5443,5500,5562,5616,5688,5823,5906,5985,6081,6164,6242,6383,6467,6549,6697,6787,6865,6918,6977,7043,7114,7193,7264,7347,7423,7501,7573,7646,7750,7839,7911,8005,8104,8178,8250,8351,8401,8486,8552,8642,8731,8793,8857,8920,8987,9103,9216,9325,9430,9487,9550,9715,9800,9874", "endLines": "5,33,34,35,36,37,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,113,114,115", "endColumns": "12,78,77,85,99,91,100,125,82,64,64,99,69,58,97,61,63,58,71,62,53,116,56,61,53,71,134,82,78,95,82,77,140,83,81,147,89,77,52,58,65,70,78,70,82,75,77,71,72,103,88,71,93,98,73,71,100,49,84,65,89,88,61,63,62,66,115,112,108,104,56,62,82,84,73,77", "endOffsets": "312,3087,3165,3251,3351,3443,4281,4407,4490,4555,4620,4720,4790,4849,4947,5009,5073,5132,5204,5267,5321,5438,5495,5557,5611,5683,5818,5901,5980,6076,6159,6237,6378,6462,6544,6692,6782,6860,6913,6972,7038,7109,7188,7259,7342,7418,7496,7568,7641,7745,7834,7906,8000,8099,8173,8245,8346,8396,8481,8547,8637,8726,8788,8852,8915,8982,9098,9211,9320,9425,9482,9545,9628,9795,9869,9947"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\bd0f378145676ccc32210984b212ca64\\transformed\\appcompat-1.7.1\\res\\values-sq\\values-sq.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,219,319,431,517,623,746,828,906,997,1090,1185,1279,1380,1473,1568,1665,1756,1849,1930,2036,2140,2238,2344,2448,2550,2704,2801", "endColumns": "113,99,111,85,105,122,81,77,90,92,94,93,100,92,94,96,90,92,80,105,103,97,105,103,101,153,96,81", "endOffsets": "214,314,426,512,618,741,823,901,992,1085,1180,1274,1375,1468,1563,1660,1751,1844,1925,2031,2135,2233,2339,2443,2545,2699,2796,2878"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,112", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "317,431,531,643,729,835,958,1040,1118,1209,1302,1397,1491,1592,1685,1780,1877,1968,2061,2142,2248,2352,2450,2556,2660,2762,2916,9633", "endColumns": "113,99,111,85,105,122,81,77,90,92,94,93,100,92,94,96,90,92,80,105,103,97,105,103,101,153,96,81", "endOffsets": "426,526,638,724,830,953,1035,1113,1204,1297,1392,1486,1587,1680,1775,1872,1963,2056,2137,2243,2347,2445,2551,2655,2757,2911,3008,9710"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\11986f19adc31a9b38ad6a88093262d6\\transformed\\core-1.13.0\\res\\values-sq\\values-sq.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,154,256,354,451,559,670,792", "endColumns": "98,101,97,96,107,110,121,100", "endOffsets": "149,251,349,446,554,665,787,888"}, "to": {"startLines": "38,39,40,41,42,43,44,116", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3448,3547,3649,3747,3844,3952,4063,9952", "endColumns": "98,101,97,96,107,110,121,100", "endOffsets": "3542,3644,3742,3839,3947,4058,4180,10048"}}]}]}