import requests

cookies = {
    '__cf_bm': '6RQ0F9O9g0LgEAkugUrDNlSZTJiIpv3IaK0a037p1VA-1756794702-*******-qlpbQlALBngU2dAWwKjVQAxH93CxsoxrTJTQDClLD0.hYBC3d2GlA2H8swG51D4h_rJU9zJtF9PK3vMNVBBN3pMen9s7UOhi24nPrH7O0Cc',
}

headers = {
    'accept': '*/*',
    'accept-language': 'zh-CN,zh;q=0.9',
    'content-type': 'text/plain',
    'origin': 'https://www.diabrowser.com',
    'priority': 'u=1, i',
    'referer': 'https://www.diabrowser.com/invite/I56I34U',
    'sec-ch-ua': '"Not;A=Brand";v="99", "Microsoft Edge";v="139", "Chromium";v="139"',
    'sec-ch-ua-mobile': '?0',
    'sec-ch-ua-platform': '"Windows"',
    'sec-fetch-dest': 'empty',
    'sec-fetch-mode': 'cors',
    'sec-fetch-site': 'same-origin',
    'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    # 'cookie': '__cf_bm=6RQ0F9O9g0LgEAkugUrDNlSZTJiIpv3IaK0a037p1VA-1756794702-*******-qlpbQlALBngU2dAWwKjVQAxH93CxsoxrTJTQDClLD0.hYBC3d2GlA2H8swG51D4h_rJU9zJtF9PK3vMNVBBN3pMen9s7UOhi24nPrH7O0Cc',
}

data = '{"n":"engagement","sd":10,"d":"diabrowser.com","u":"https://www.diabrowser.com/invite/I56I34U","p":{"is_dia":false},"e":11723,"v":30}'

response = requests.post('https://www.diabrowser.com/api/event', cookies=cookies, headers=headers, data=data)

print(response.text)