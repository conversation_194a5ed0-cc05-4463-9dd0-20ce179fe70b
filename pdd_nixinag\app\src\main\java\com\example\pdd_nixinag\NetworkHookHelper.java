package com.example.pdd_nixinag;

import android.util.Log;
import de.robv.android.xposed.XC_MethodHook;
import de.robv.android.xposed.XposedBridge;
import de.robv.android.xposed.XposedHelpers;
import de.robv.android.xposed.callbacks.XC_LoadPackage;

import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.lang.reflect.Method;
import java.nio.charset.StandardCharsets;

/**
 * 网络Hook辅助类
 * 提供更全面的网络请求拦截功能
 */
public class NetworkHookHelper {

    private static final String TAG = "PDD_NetworkHelper";

    /**
     * Hook所有可能的网络响应处理方法
     */
    public static void hookAllNetworkMethods(XC_LoadPackage.LoadPackageParam lpparam) {
        // Hook CommonCallback回调
        hookCommonCallback(lpparam);

        // Hook BaseCallback回调
        hookBaseCallback(lpparam);

        // Hook ResponseBody处理
        hookResponseBody(lpparam);

        // Hook InputStream读取
        hookInputStreamRead(lpparam);

        // Hook字节数组处理
        hookByteArrayProcessing(lpparam);
    }

    /**
     * Hook CommonCallback的响应处理
     */
    private static void hookCommonCallback(XC_LoadPackage.LoadPackageParam lpparam) {
        try {
            Class<?> commonCallbackClass = XposedHelpers.findClass(
                "com.xunmeng.pinduoduo.basekit.http.callback.CommonCallback",
                lpparam.classLoader
            );

            // Hook onSuccess方法
            XposedHelpers.findAndHookMethod(commonCallbackClass, "onSuccess",
                String.class, new XC_MethodHook() {
                @Override
                protected void beforeHookedMethod(MethodHookParam param) throws Throwable {
                    try {
                        String response = (String) param.args[0];
                        if (response != null && !response.isEmpty()) {
                            logResponse("CommonCallback.onSuccess", "Unknown", 200, response);
                        }
                    } catch (Exception e) {
                        XposedBridge.log(TAG + ": Hook CommonCallback.onSuccess异常: " + e.getMessage());
                    }
                }
            });

            // Hook onFailure方法
            XposedHelpers.findAndHookMethod(commonCallbackClass, "onFailure",
                int.class, String.class, new XC_MethodHook() {
                @Override
                protected void beforeHookedMethod(MethodHookParam param) throws Throwable {
                    try {
                        int errorCode = (Integer) param.args[0];
                        String errorMsg = (String) param.args[1];
                        logResponse("CommonCallback.onFailure", "Unknown", errorCode,
                            "错误信息: " + errorMsg);
                    } catch (Exception e) {
                        XposedBridge.log(TAG + ": Hook CommonCallback.onFailure异常: " + e.getMessage());
                    }
                }
            });

        } catch (Exception e) {
            XposedBridge.log(TAG + ": Hook CommonCallback失败: " + e.getMessage());
        }
    }

    /**
     * Hook BaseCallback的响应处理
     */
    private static void hookBaseCallback(XC_LoadPackage.LoadPackageParam lpparam) {
        try {
            Class<?> baseCallbackClass = XposedHelpers.findClass(
                "com.xunmeng.pinduoduo.basekit.http.callback.BaseCallback",
                lpparam.classLoader
            );

            // 获取所有方法并Hook可能的回调方法
            Method[] methods = baseCallbackClass.getDeclaredMethods();
            for (Method method : methods) {
                if (method.getName().startsWith("on") && method.getParameterCount() > 0) {
                    XposedBridge.hookMethod(method, new XC_MethodHook() {
                        @Override
                        protected void beforeHookedMethod(MethodHookParam param) throws Throwable {
                            try {
                                Object[] args = param.args;
                                if (args != null && args.length > 0) {
                                    for (Object arg : args) {
                                        if (arg instanceof String && ((String) arg).length() > 10) {
                                            logResponse("BaseCallback." + method.getName(),
                                                "Unknown", 0, (String) arg);
                                        }
                                    }
                                }
                            } catch (Exception e) {
                                XposedBridge.log(TAG + ": Hook BaseCallback方法异常: " + e.getMessage());
                            }
                        }
                    });
                }
            }

        } catch (Exception e) {
            XposedBridge.log(TAG + ": Hook BaseCallback失败: " + e.getMessage());
        }
    }

    /**
     * Hook ResponseBody的处理
     */
    private static void hookResponseBody(XC_LoadPackage.LoadPackageParam lpparam) {
        try {
            Class<?> responseBodyClass = XposedHelpers.findClass("okhttp3.ResponseBody", lpparam.classLoader);

            // Hook string()方法
            XposedHelpers.findAndHookMethod(responseBodyClass, "string", new XC_MethodHook() {
                @Override
                protected void afterHookedMethod(MethodHookParam param) throws Throwable {
                    try {
                        String content = (String) param.getResult();
                        if (content != null && !content.isEmpty()) {
                            logResponse("ResponseBody.string", "Unknown", 200, content);
                        }
                    } catch (Exception e) {
                        XposedBridge.log(TAG + ": Hook ResponseBody.string异常: " + e.getMessage());
                    }
                }
            });

            // Hook bytes()方法
            XposedHelpers.findAndHookMethod(responseBodyClass, "bytes", new XC_MethodHook() {
                @Override
                protected void afterHookedMethod(MethodHookParam param) throws Throwable {
                    try {
                        byte[] bytes = (byte[]) param.getResult();
                        if (bytes != null && bytes.length > 0) {
                            String content = new String(bytes, StandardCharsets.UTF_8);
                            logResponse("ResponseBody.bytes", "Unknown", 200, content);
                        }
                    } catch (Exception e) {
                        XposedBridge.log(TAG + ": Hook ResponseBody.bytes异常: " + e.getMessage());
                    }
                }
            });

        } catch (Exception e) {
            XposedBridge.log(TAG + ": Hook ResponseBody失败: " + e.getMessage());
        }
    }

    /**
     * Hook InputStream的读取操作
     */
    private static void hookInputStreamRead(XC_LoadPackage.LoadPackageParam lpparam) {
        try {
            Class<?> inputStreamClass = XposedHelpers.findClass("java.io.InputStream", lpparam.classLoader);

            // Hook read(byte[])方法
            XposedHelpers.findAndHookMethod(inputStreamClass, "read", byte[].class, new XC_MethodHook() {
                @Override
                protected void afterHookedMethod(MethodHookParam param) throws Throwable {
                    try {
                        int bytesRead = (Integer) param.getResult();
                        if (bytesRead > 0) {
                            byte[] buffer = (byte[]) param.args[0];
                            if (buffer != null && buffer.length > 0) {
                                // 只处理看起来像HTTP响应的数据
                                String content = new String(buffer, 0, bytesRead, StandardCharsets.UTF_8);
                                if (isHttpResponse(content)) {
                                    logResponse("InputStream.read", "Unknown", 200, content);
                                }
                            }
                        }
                    } catch (Exception e) {
                        // 忽略InputStream读取的异常，避免日志过多
                    }
                }
            });

        } catch (Exception e) {
            XposedBridge.log(TAG + ": Hook InputStream失败: " + e.getMessage());
        }
    }

    /**
     * Hook字节数组处理相关方法
     */
    private static void hookByteArrayProcessing(XC_LoadPackage.LoadPackageParam lpparam) {
        try {
            // Hook String构造函数，捕获从字节数组创建的字符串
            Class<?> stringClass = XposedHelpers.findClass("java.lang.String", lpparam.classLoader);

            XposedHelpers.findAndHookConstructor(stringClass, byte[].class, String.class, new XC_MethodHook() {
                @Override
                protected void afterHookedMethod(MethodHookParam param) throws Throwable {
                    try {
                        String result = (String) param.thisObject;
                        if (result != null && isHttpResponse(result)) {
                            logResponse("String构造(byte[])", "Unknown", 200, result);
                        }
                    } catch (Exception e) {
                        // 忽略String构造的异常，避免日志过多
                    }
                }
            });

        } catch (Exception e) {
            XposedBridge.log(TAG + ": Hook字节数组处理失败: " + e.getMessage());
        }
    }

    /**
     * 判断内容是否像HTTP响应
     */
    private static boolean isHttpResponse(String content) {
        if (content == null || content.length() < 10) {
            return false;
        }

        // 检查是否包含JSON格式
        if (content.trim().startsWith("{") && content.trim().endsWith("}")) {
            return true;
        }

        // 检查是否包含XML格式
        if (content.trim().startsWith("<") && content.trim().endsWith(">")) {
            return true;
        }

        // 检查是否包含常见的API响应字段
        return content.contains("\"code\"") ||
               content.contains("\"data\"") ||
               content.contains("\"result\"") ||
               content.contains("\"success\"") ||
               content.contains("\"error\"") ||
               content.contains("\"message\"");
    }

    /**
     * 统一的响应日志记录
     */
    private static void logResponse(String source, String url, int statusCode, String content) {
        try {
            // 限制内容长度，避免日志过长
            String truncatedContent = content;
            if (content.length() > 2000) {
                truncatedContent = content.substring(0, 2000) + "\n... (内容被截断，总长度: " + content.length() + " 字符)";
            }

            String logMessage = String.format(
                "\n=== 拼多多网络响应 [%s] ===\n" +
                "URL: %s\n" +
                "状态码: %d\n" +
                "响应长度: %d 字符\n" +
                "响应内容:\n%s\n" +
                "================================\n",
                source, url, statusCode, content.length(), truncatedContent
            );

            // 输出到Xposed日志和Android日志
            XposedBridge.log(TAG + ": " + logMessage);
            Log.d(TAG, logMessage);

        } catch (Exception e) {
            XposedBridge.log(TAG + ": 记录响应日志失败: " + e.getMessage());
        }
    }

    /**
     * 从InputStream读取所有内容
     */
    private static String readInputStream(InputStream inputStream) {
        try {
            ByteArrayOutputStream result = new ByteArrayOutputStream();
            byte[] buffer = new byte[1024];
            int length;
            while ((length = inputStream.read(buffer)) != -1) {
                result.write(buffer, 0, length);
            }
            return result.toString(StandardCharsets.UTF_8.name());
        } catch (Exception e) {
            XposedBridge.log(TAG + ": 读取InputStream失败: " + e.getMessage());
            return null;
        }
    }
}