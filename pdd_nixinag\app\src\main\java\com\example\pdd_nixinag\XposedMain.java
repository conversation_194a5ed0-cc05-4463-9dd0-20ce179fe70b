package com.example.pdd_nixinag;

import android.util.Log;
import de.robv.android.xposed.IXposedHookLoadPackage;
import de.robv.android.xposed.XC_MethodHook;
import de.robv.android.xposed.XposedBridge;
import de.robv.android.xposed.XposedHelpers;
import de.robv.android.xposed.callbacks.XC_LoadPackage;

import java.io.IOException;
import java.lang.reflect.Method;
import java.nio.charset.StandardCharsets;

/**
 * 拼多多网络请求Hook模块
 * 主要Hook目标：
 * 1. OkHttp的Response处理
 * 2. HttpCall的响应回调
 * 3. QuickCall的网络请求
 * 4. 各种拦截器的响应处理
 */
public class XposedMain implements IXposedHookLoadPackage {
    
    private static final String TAG = "PDD_NetworkHook";
    private static final String TARGET_PACKAGE = "com.xunmeng.pinduoduo";
    
    @Override
    public void handleLoadPackage(XC_LoadPackage.LoadPackageParam lpparam) throws Throwable {
        // 只Hook拼多多应用
        if (!TARGET_PACKAGE.equals(lpparam.packageName)) {
            return;
        }
        
        XposedBridge.log(TAG + ": 开始Hook拼多多应用 - " + lpparam.packageName);
        
        // Hook OkHttp Response
        hookOkHttpResponse(lpparam);
        
        // Hook HttpCall响应
        hookHttpCallResponse(lpparam);
        
        // Hook QuickCall响应
        hookQuickCallResponse(lpparam);
        
        // Hook拦截器响应
        hookInterceptorResponse(lpparam);
        
        // Hook HttpHandler回调
        hookHttpHandlerCallback(lpparam);

        // 使用NetworkHookHelper进行更全面的Hook
        NetworkHookHelper.hookAllNetworkMethods(lpparam);

        // Hook拼多多特定的网络类
        hookPddSpecificClasses(lpparam);
    }
    
    /**
     * Hook OkHttp的Response处理
     */
    private void hookOkHttpResponse(XC_LoadPackage.LoadPackageParam lpparam) {
        try {
            // Hook okhttp3.Response的body()方法
            Class<?> responseClass = XposedHelpers.findClass("okhttp3.Response", lpparam.classLoader);
            XposedHelpers.findAndHookMethod(responseClass, "body", new XC_MethodHook() {
                @Override
                protected void afterHookedMethod(MethodHookParam param) throws Throwable {
                    try {
                        Object response = param.thisObject;
                        Object responseBody = param.getResult();
                        
                        if (responseBody != null) {
                            // 获取请求URL
                            Object request = XposedHelpers.callMethod(response, "request");
                            Object url = XposedHelpers.callMethod(request, "url");
                            String urlString = url.toString();
                            
                            // 获取响应码
                            int code = (Integer) XposedHelpers.callMethod(response, "code");
                            
                            // 尝试读取响应体内容
                            try {
                                Object source = XposedHelpers.callMethod(responseBody, "source");
                                if (source != null) {
                                    byte[] bytes = (byte[]) XposedHelpers.callMethod(source, "readByteArray");
                                    if (bytes != null && bytes.length > 0) {
                                        String responseContent = new String(bytes, StandardCharsets.UTF_8);
                                        logNetworkResponse("OkHttp", urlString, code, responseContent);
                                    }
                                }
                            } catch (Exception e) {
                                XposedBridge.log(TAG + ": 读取OkHttp响应体失败: " + e.getMessage());
                            }
                        }
                    } catch (Exception e) {
                        XposedBridge.log(TAG + ": Hook OkHttp Response异常: " + e.getMessage());
                    }
                }
            });
        } catch (Exception e) {
            XposedBridge.log(TAG + ": Hook OkHttp Response失败: " + e.getMessage());
        }
    }
    
    /**
     * Hook HttpCall的响应处理
     */
    private void hookHttpCallResponse(XC_LoadPackage.LoadPackageParam lpparam) {
        try {
            // Hook HttpCall的execute方法
            Class<?> httpCallClass = XposedHelpers.findClass("com.aimi.android.common.http.HttpCall", lpparam.classLoader);
            
            // Hook call()方法 - 同步调用
            XposedHelpers.findAndHookMethod(httpCallClass, "call", new XC_MethodHook() {
                @Override
                protected void afterHookedMethod(MethodHookParam param) throws Throwable {
                    try {
                        Object httpCall = param.thisObject;
                        String result = (String) param.getResult();
                        
                        if (result != null) {
                            String url = (String) XposedHelpers.callMethod(httpCall, "getUrl");
                            logNetworkResponse("HttpCall.call", url, 200, result);
                        }
                    } catch (Exception e) {
                        XposedBridge.log(TAG + ": Hook HttpCall.call异常: " + e.getMessage());
                    }
                }
            });
            
        } catch (Exception e) {
            XposedBridge.log(TAG + ": Hook HttpCall失败: " + e.getMessage());
        }
    }
    
    /**
     * Hook QuickCall的响应处理
     */
    private void hookQuickCallResponse(XC_LoadPackage.LoadPackageParam lpparam) {
        try {
            // Hook QuickCall的响应处理
            Class<?> quickCallClass = XposedHelpers.findClass("com.xunmeng.pinduoduo.arch.quickcall.QuickCall", lpparam.classLoader);
            
            // 查找所有方法并Hook可能的响应处理方法
            Method[] methods = quickCallClass.getDeclaredMethods();
            for (Method method : methods) {
                if (method.getName().contains("onResponse") || method.getName().contains("callback")) {
                    XposedBridge.hookMethod(method, new XC_MethodHook() {
                        @Override
                        protected void beforeHookedMethod(MethodHookParam param) throws Throwable {
                            try {
                                Object[] args = param.args;
                                if (args != null && args.length > 0) {
                                    for (Object arg : args) {
                                        if (arg != null && arg.toString().length() > 10) {
                                            logNetworkResponse("QuickCall." + method.getName(), "Unknown", 0, arg.toString());
                                        }
                                    }
                                }
                            } catch (Exception e) {
                                XposedBridge.log(TAG + ": Hook QuickCall方法异常: " + e.getMessage());
                            }
                        }
                    });
                }
            }
            
        } catch (Exception e) {
            XposedBridge.log(TAG + ": Hook QuickCall失败: " + e.getMessage());
        }
    }
    
    /**
     * Hook拦截器的响应处理
     */
    private void hookInterceptorResponse(XC_LoadPackage.LoadPackageParam lpparam) {
        try {
            // Hook WrapperInterceptor
            Class<?> wrapperInterceptorClass = XposedHelpers.findClass(
                "com.xunmeng.pinduoduo.net_adapter.hera.interceptors.WrapperInterceptor", 
                lpparam.classLoader
            );
            
            XposedHelpers.findAndHookMethod(wrapperInterceptorClass, "intercept", 
                "okhttp3.Interceptor$Chain", new XC_MethodHook() {
                @Override
                protected void afterHookedMethod(MethodHookParam param) throws Throwable {
                    try {
                        Object response = param.getResult();
                        if (response != null) {
                            Object request = XposedHelpers.callMethod(response, "request");
                            Object url = XposedHelpers.callMethod(request, "url");
                            int code = (Integer) XposedHelpers.callMethod(response, "code");
                            
                            logNetworkResponse("WrapperInterceptor", url.toString(), code, "拦截器响应");
                        }
                    } catch (Exception e) {
                        XposedBridge.log(TAG + ": Hook WrapperInterceptor异常: " + e.getMessage());
                    }
                }
            });
            
        } catch (Exception e) {
            XposedBridge.log(TAG + ": Hook拦截器失败: " + e.getMessage());
        }
    }
    
    /**
     * Hook HttpHandler的回调处理
     */
    private void hookHttpHandlerCallback(XC_LoadPackage.LoadPackageParam lpparam) {
        try {
            // Hook HttpHandler的onResponse回调
            Class<?> httpHandlerClass = XposedHelpers.findClass(
                "com.xunmeng.pinduoduo.pddmap.networking.HttpHandler$InterfaceC10784a", 
                lpparam.classLoader
            );
            
            XposedHelpers.findAndHookMethod(httpHandlerClass, "onResponse", 
                int.class, byte[].class, new XC_MethodHook() {
                @Override
                protected void beforeHookedMethod(MethodHookParam param) throws Throwable {
                    try {
                        int statusCode = (Integer) param.args[0];
                        byte[] responseData = (byte[]) param.args[1];
                        
                        if (responseData != null && responseData.length > 0) {
                            String responseContent = new String(responseData, StandardCharsets.UTF_8);
                            logNetworkResponse("HttpHandler", "Unknown", statusCode, responseContent);
                        }
                    } catch (Exception e) {
                        XposedBridge.log(TAG + ": Hook HttpHandler回调异常: " + e.getMessage());
                    }
                }
            });
            
        } catch (Exception e) {
            XposedBridge.log(TAG + ": Hook HttpHandler失败: " + e.getMessage());
        }
    }
    
    /**
     * Hook拼多多特定的网络类
     */
    private void hookPddSpecificClasses(XC_LoadPackage.LoadPackageParam lpparam) {
        // Hook AMNetworkImpl
        hookAMNetworkImpl(lpparam);

        // Hook SecurityHttpHandler
        hookSecurityHttpHandler(lpparam);

        // Hook各种拦截器
        hookPddInterceptors(lpparam);
    }

    /**
     * Hook AMNetworkImpl网络实现
     */
    private void hookAMNetworkImpl(XC_LoadPackage.LoadPackageParam lpparam) {
        try {
            Class<?> amNetworkClass = XposedHelpers.findClass(
                "com.xunmeng.pinduoduo.network_bridge.impl.AMNetworkImpl",
                lpparam.classLoader
            );

            // Hook callbackToJS方法
            XposedHelpers.findAndHookMethod(amNetworkClass, "callbackToJS",
                int.class, Object.class, String.class, boolean.class, new XC_MethodHook() {
                @Override
                protected void beforeHookedMethod(MethodHookParam param) throws Throwable {
                    try {
                        int statusCode = (Integer) param.args[0];
                        String response = (String) param.args[2];

                        if (response != null && !response.isEmpty()) {
                            logNetworkResponse("AMNetworkImpl.callbackToJS", "Unknown", statusCode, response);
                        }
                    } catch (Exception e) {
                        XposedBridge.log(TAG + ": Hook AMNetworkImpl异常: " + e.getMessage());
                    }
                }
            });

        } catch (Exception e) {
            XposedBridge.log(TAG + ": Hook AMNetworkImpl失败: " + e.getMessage());
        }
    }

    /**
     * Hook SecurityHttpHandler
     */
    private void hookSecurityHttpHandler(XC_LoadPackage.LoadPackageParam lpparam) {
        try {
            Class<?> securityHttpHandlerClass = XposedHelpers.findClass(
                "com.xunmeng.pinduoduo.map.base.utils.SecurityHttpHandler",
                lpparam.classLoader
            );

            // Hook startRequest方法
            XposedHelpers.findAndHookMethod(securityHttpHandlerClass, "startRequest",
                String.class, Object.class, boolean.class, new XC_MethodHook() {
                @Override
                protected void beforeHookedMethod(MethodHookParam param) throws Throwable {
                    try {
                        String url = (String) param.args[0];
                        XposedBridge.log(TAG + ": SecurityHttpHandler请求URL: " + url);
                    } catch (Exception e) {
                        XposedBridge.log(TAG + ": Hook SecurityHttpHandler异常: " + e.getMessage());
                    }
                }
            });

        } catch (Exception e) {
            XposedBridge.log(TAG + ": Hook SecurityHttpHandler失败: " + e.getMessage());
        }
    }

    /**
     * Hook拼多多的各种拦截器
     */
    private void hookPddInterceptors(XC_LoadPackage.LoadPackageParam lpparam) {
        // Hook各种拦截器类
        String[] interceptorClasses = {
            "com.aimi.android.common.http.unity.internal.interceptor.C3223a",
            "com.aimi.android.common.http.unity.internal.interceptor.C3224b",
            "com.aimi.android.common.http.unity.internal.interceptor.C3225c",
            "com.aimi.android.common.http.unity.internal.interceptor.C3226d",
            "com.aimi.android.common.http.unity.internal.interceptor.C3227e",
            "com.aimi.android.common.http.unity.internal.interceptor.C3230h",
            "com.aimi.android.common.http.unity.internal.interceptor.C3231i",
            "com.aimi.android.common.http.unity.internal.interceptor.C3232j",
            "com.aimi.android.common.http.unity.internal.interceptor.C3233k"
        };

        for (String className : interceptorClasses) {
            try {
                Class<?> interceptorClass = XposedHelpers.findClass(className, lpparam.classLoader);

                // Hook intercept方法
                XposedHelpers.findAndHookMethod(interceptorClass, "intercept",
                    "okhttp3.Interceptor$Chain", new XC_MethodHook() {
                    @Override
                    protected void afterHookedMethod(MethodHookParam param) throws Throwable {
                        try {
                            Object response = param.getResult();
                            if (response != null) {
                                // 尝试获取响应信息
                                Object request = XposedHelpers.callMethod(response, "request");
                                Object url = XposedHelpers.callMethod(request, "url");
                                int code = (Integer) XposedHelpers.callMethod(response, "code");

                                String interceptorName = className.substring(className.lastIndexOf('.') + 1);
                                XposedBridge.log(TAG + ": " + interceptorName + " 拦截器处理: " +
                                    url.toString() + " (状态码: " + code + ")");
                            }
                        } catch (Exception e) {
                            // 忽略拦截器Hook的异常
                        }
                    }
                });

            } catch (Exception e) {
                // 某些拦截器类可能不存在，忽略异常
            }
        }
    }

    /**
     * 统一的网络响应日志记录
     */
    private void logNetworkResponse(String source, String url, int statusCode, String responseContent) {
        try {
            String logMessage = String.format(
                "\n=== 拼多多网络请求响应 [%s] ===\n" +
                "URL: %s\n" +
                "状态码: %d\n" +
                "响应长度: %d 字节\n" +
                "响应内容: %s\n" +
                "================================\n",
                source, url, statusCode, responseContent.length(),
                responseContent.length() > 1000 ? responseContent.substring(0, 1000) + "..." : responseContent
            );

            // 同时输出到Xposed日志和Android日志
            XposedBridge.log(TAG + ": " + logMessage);
            Log.d(TAG, logMessage);

        } catch (Exception e) {
            XposedBridge.log(TAG + ": 记录网络响应日志失败: " + e.getMessage());
        }
    }
}
