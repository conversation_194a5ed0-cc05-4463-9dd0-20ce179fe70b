{"logs": [{"outputFile": "com.example.pdd_nixinag.app-mergeDebugResources-29:/values-ko/values-ko.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\11986f19adc31a9b38ad6a88093262d6\\transformed\\core-1.13.0\\res\\values-ko\\values-ko.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,147,247,341,438,534,632,732", "endColumns": "91,99,93,96,95,97,99,100", "endOffsets": "142,242,336,433,529,627,727,828"}, "to": {"startLines": "38,39,40,41,42,43,44,116", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3230,3322,3422,3516,3613,3709,3807,8975", "endColumns": "91,99,93,96,95,97,99,100", "endOffsets": "3317,3417,3511,3608,3704,3802,3902,9071"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\7146d326271694f6c4b3d2404ca60108\\transformed\\material-1.12.0\\res\\values-ko\\values-ko.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,249,314,378,447,521,600,683,789,864,917,979,1060,1122,1179,1266,1326,1384,1442,1501,1558,1612,1707,1763,1820,1874,1940,2044,2119,2191,2272,2350,2427,2548,2613,2678,2778,2857,2932,2982,3033,3099,3163,3233,3304,3375,3443,3514,3586,3656,3749,3829,3903,3983,4065,4137,4202,4274,4322,4395,4459,4534,4611,4673,4737,4800,4867,4951,5029,5109,5187,5241,5296,5368,5445,5518", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,64,63,68,73,78,82,105,74,52,61,80,61,56,86,59,57,57,58,56,53,94,55,56,53,65,103,74,71,80,77,76,120,64,64,99,78,74,49,50,65,63,69,70,70,67,70,71,69,92,79,73,79,81,71,64,71,47,72,63,74,76,61,63,62,66,83,77,79,77,53,54,71,76,72,70", "endOffsets": "244,309,373,442,516,595,678,784,859,912,974,1055,1117,1174,1261,1321,1379,1437,1496,1553,1607,1702,1758,1815,1869,1935,2039,2114,2186,2267,2345,2422,2543,2608,2673,2773,2852,2927,2977,3028,3094,3158,3228,3299,3370,3438,3509,3581,3651,3744,3824,3898,3978,4060,4132,4197,4269,4317,4390,4454,4529,4606,4668,4732,4795,4862,4946,5024,5104,5182,5236,5291,5363,5440,5513,5584"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,113,114,115", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,2879,2944,3008,3077,3151,3907,3990,4096,4171,4224,4286,4367,4429,4486,4573,4633,4691,4749,4808,4865,4919,5014,5070,5127,5181,5247,5351,5426,5498,5579,5657,5734,5855,5920,5985,6085,6164,6239,6289,6340,6406,6470,6540,6611,6682,6750,6821,6893,6963,7056,7136,7210,7290,7372,7444,7509,7581,7629,7702,7766,7841,7918,7980,8044,8107,8174,8258,8336,8416,8494,8548,8603,8754,8831,8904", "endLines": "5,33,34,35,36,37,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,113,114,115", "endColumns": "12,64,63,68,73,78,82,105,74,52,61,80,61,56,86,59,57,57,58,56,53,94,55,56,53,65,103,74,71,80,77,76,120,64,64,99,78,74,49,50,65,63,69,70,70,67,70,71,69,92,79,73,79,81,71,64,71,47,72,63,74,76,61,63,62,66,83,77,79,77,53,54,71,76,72,70", "endOffsets": "294,2939,3003,3072,3146,3225,3985,4091,4166,4219,4281,4362,4424,4481,4568,4628,4686,4744,4803,4860,4914,5009,5065,5122,5176,5242,5346,5421,5493,5574,5652,5729,5850,5915,5980,6080,6159,6234,6284,6335,6401,6465,6535,6606,6677,6745,6816,6888,6958,7051,7131,7205,7285,7367,7439,7504,7576,7624,7697,7761,7836,7913,7975,8039,8102,8169,8253,8331,8411,8489,8543,8598,8670,8826,8899,8970"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\bd0f378145676ccc32210984b212ca64\\transformed\\appcompat-1.7.1\\res\\values-ko\\values-ko.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,202,296,397,479,577,683,763,838,929,1022,1117,1211,1311,1404,1499,1593,1684,1775,1855,1953,2047,2142,2242,2339,2439,2591,2685", "endColumns": "96,93,100,81,97,105,79,74,90,92,94,93,99,92,94,93,90,90,79,97,93,94,99,96,99,151,93,78", "endOffsets": "197,291,392,474,572,678,758,833,924,1017,1112,1206,1306,1399,1494,1588,1679,1770,1850,1948,2042,2137,2237,2334,2434,2586,2680,2759"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,112", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "299,396,490,591,673,771,877,957,1032,1123,1216,1311,1405,1505,1598,1693,1787,1878,1969,2049,2147,2241,2336,2436,2533,2633,2785,8675", "endColumns": "96,93,100,81,97,105,79,74,90,92,94,93,99,92,94,93,90,90,79,97,93,94,99,96,99,151,93,78", "endOffsets": "391,485,586,668,766,872,952,1027,1118,1211,1306,1400,1500,1593,1688,1782,1873,1964,2044,2142,2236,2331,2431,2528,2628,2780,2874,8749"}}]}]}