1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.pdd_nixinag"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="34" />
10
11    <!-- Xposed Module Metadata -->
12    <meta-data
12-->D:\桌面\拼多多逆向\pdd_nixinag\app\src\main\AndroidManifest.xml:6:5-8:32
13        android:name="xposedmodule"
13-->D:\桌面\拼多多逆向\pdd_nixinag\app\src\main\AndroidManifest.xml:7:9-36
14        android:value="true" />
14-->D:\桌面\拼多多逆向\pdd_nixinag\app\src\main\AndroidManifest.xml:8:9-29
15    <meta-data
15-->D:\桌面\拼多多逆向\pdd_nixinag\app\src\main\AndroidManifest.xml:9:5-11:57
16        android:name="xposeddescription"
16-->D:\桌面\拼多多逆向\pdd_nixinag\app\src\main\AndroidManifest.xml:10:9-41
17        android:value="拼多多网络请求Hook模块 - 捕获并打印网络请求响应数据" />
17-->D:\桌面\拼多多逆向\pdd_nixinag\app\src\main\AndroidManifest.xml:11:9-54
18    <meta-data
18-->D:\桌面\拼多多逆向\pdd_nixinag\app\src\main\AndroidManifest.xml:12:5-14:30
19        android:name="xposedminversion"
19-->D:\桌面\拼多多逆向\pdd_nixinag\app\src\main\AndroidManifest.xml:13:9-40
20        android:value="54" />
20-->D:\桌面\拼多多逆向\pdd_nixinag\app\src\main\AndroidManifest.xml:14:9-27
21
22    <permission
22-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\11986f19adc31a9b38ad6a88093262d6\transformed\core-1.13.0\AndroidManifest.xml:22:5-24:47
23        android:name="com.example.pdd_nixinag.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
23-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\11986f19adc31a9b38ad6a88093262d6\transformed\core-1.13.0\AndroidManifest.xml:23:9-81
24        android:protectionLevel="signature" />
24-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\11986f19adc31a9b38ad6a88093262d6\transformed\core-1.13.0\AndroidManifest.xml:24:9-44
25
26    <uses-permission android:name="com.example.pdd_nixinag.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
26-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\11986f19adc31a9b38ad6a88093262d6\transformed\core-1.13.0\AndroidManifest.xml:26:5-97
26-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\11986f19adc31a9b38ad6a88093262d6\transformed\core-1.13.0\AndroidManifest.xml:26:22-94
27
28    <application
28-->D:\桌面\拼多多逆向\pdd_nixinag\app\src\main\AndroidManifest.xml:16:5-35:19
29        android:allowBackup="true"
29-->D:\桌面\拼多多逆向\pdd_nixinag\app\src\main\AndroidManifest.xml:17:9-35
30        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
30-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\11986f19adc31a9b38ad6a88093262d6\transformed\core-1.13.0\AndroidManifest.xml:28:18-86
31        android:dataExtractionRules="@xml/data_extraction_rules"
31-->D:\桌面\拼多多逆向\pdd_nixinag\app\src\main\AndroidManifest.xml:18:9-65
32        android:debuggable="true"
33        android:extractNativeLibs="false"
34        android:fullBackupContent="@xml/backup_rules"
34-->D:\桌面\拼多多逆向\pdd_nixinag\app\src\main\AndroidManifest.xml:19:9-54
35        android:icon="@mipmap/ic_launcher"
35-->D:\桌面\拼多多逆向\pdd_nixinag\app\src\main\AndroidManifest.xml:20:9-43
36        android:label="@string/app_name"
36-->D:\桌面\拼多多逆向\pdd_nixinag\app\src\main\AndroidManifest.xml:21:9-41
37        android:roundIcon="@mipmap/ic_launcher_round"
37-->D:\桌面\拼多多逆向\pdd_nixinag\app\src\main\AndroidManifest.xml:22:9-54
38        android:supportsRtl="true"
38-->D:\桌面\拼多多逆向\pdd_nixinag\app\src\main\AndroidManifest.xml:23:9-35
39        android:theme="@style/Theme.Pdd_nixinag" >
39-->D:\桌面\拼多多逆向\pdd_nixinag\app\src\main\AndroidManifest.xml:24:9-49
40        <activity
40-->D:\桌面\拼多多逆向\pdd_nixinag\app\src\main\AndroidManifest.xml:26:9-33:20
41            android:name="com.example.pdd_nixinag.MainActivity"
41-->D:\桌面\拼多多逆向\pdd_nixinag\app\src\main\AndroidManifest.xml:27:13-41
42            android:exported="true" >
42-->D:\桌面\拼多多逆向\pdd_nixinag\app\src\main\AndroidManifest.xml:28:13-36
43            <intent-filter>
43-->D:\桌面\拼多多逆向\pdd_nixinag\app\src\main\AndroidManifest.xml:29:13-32:29
44                <action android:name="android.intent.action.MAIN" />
44-->D:\桌面\拼多多逆向\pdd_nixinag\app\src\main\AndroidManifest.xml:30:17-69
44-->D:\桌面\拼多多逆向\pdd_nixinag\app\src\main\AndroidManifest.xml:30:25-66
45
46                <category android:name="android.intent.category.LAUNCHER" />
46-->D:\桌面\拼多多逆向\pdd_nixinag\app\src\main\AndroidManifest.xml:31:17-77
46-->D:\桌面\拼多多逆向\pdd_nixinag\app\src\main\AndroidManifest.xml:31:27-74
47            </intent-filter>
48        </activity>
49
50        <provider
50-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\893d94ddcb2ba9aaeb510f8f17656d60\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
51            android:name="androidx.startup.InitializationProvider"
51-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\893d94ddcb2ba9aaeb510f8f17656d60\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
52            android:authorities="com.example.pdd_nixinag.androidx-startup"
52-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\893d94ddcb2ba9aaeb510f8f17656d60\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
53            android:exported="false" >
53-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\893d94ddcb2ba9aaeb510f8f17656d60\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
54            <meta-data
54-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\893d94ddcb2ba9aaeb510f8f17656d60\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
55                android:name="androidx.emoji2.text.EmojiCompatInitializer"
55-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\893d94ddcb2ba9aaeb510f8f17656d60\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
56                android:value="androidx.startup" />
56-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\893d94ddcb2ba9aaeb510f8f17656d60\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
57            <meta-data
57-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\0aa0becf67ab1c4904cc0751b5d07886\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
58                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
58-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\0aa0becf67ab1c4904cc0751b5d07886\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
59                android:value="androidx.startup" />
59-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\0aa0becf67ab1c4904cc0751b5d07886\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
60            <meta-data
60-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f2296df31e1c16cf0e8e6ad3edc420c9\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
61                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
61-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f2296df31e1c16cf0e8e6ad3edc420c9\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
62                android:value="androidx.startup" />
62-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f2296df31e1c16cf0e8e6ad3edc420c9\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
63        </provider>
64
65        <receiver
65-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f2296df31e1c16cf0e8e6ad3edc420c9\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
66            android:name="androidx.profileinstaller.ProfileInstallReceiver"
66-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f2296df31e1c16cf0e8e6ad3edc420c9\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
67            android:directBootAware="false"
67-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f2296df31e1c16cf0e8e6ad3edc420c9\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
68            android:enabled="true"
68-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f2296df31e1c16cf0e8e6ad3edc420c9\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
69            android:exported="true"
69-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f2296df31e1c16cf0e8e6ad3edc420c9\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
70            android:permission="android.permission.DUMP" >
70-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f2296df31e1c16cf0e8e6ad3edc420c9\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
71            <intent-filter>
71-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f2296df31e1c16cf0e8e6ad3edc420c9\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
72                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
72-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f2296df31e1c16cf0e8e6ad3edc420c9\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
72-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f2296df31e1c16cf0e8e6ad3edc420c9\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
73            </intent-filter>
74            <intent-filter>
74-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f2296df31e1c16cf0e8e6ad3edc420c9\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
75                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
75-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f2296df31e1c16cf0e8e6ad3edc420c9\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
75-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f2296df31e1c16cf0e8e6ad3edc420c9\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
76            </intent-filter>
77            <intent-filter>
77-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f2296df31e1c16cf0e8e6ad3edc420c9\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
78                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
78-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f2296df31e1c16cf0e8e6ad3edc420c9\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
78-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f2296df31e1c16cf0e8e6ad3edc420c9\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
79            </intent-filter>
80            <intent-filter>
80-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f2296df31e1c16cf0e8e6ad3edc420c9\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
81                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
81-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f2296df31e1c16cf0e8e6ad3edc420c9\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
81-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f2296df31e1c16cf0e8e6ad3edc420c9\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
82            </intent-filter>
83        </receiver>
84    </application>
85
86</manifest>
