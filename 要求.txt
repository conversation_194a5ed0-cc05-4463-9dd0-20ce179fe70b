请基于提供的 pddjava 源码和 Xposed 模板工程 pdd_nixinag，在 D:\桌面\拼多多逆向 目录下开发一个 Xposed 模块。模块需在 Android 端 Hook 拼多多 App，捕获网络的请求的响应的结果，并把原始的数据打印出来就可以了，不需要解析（打印到日志或控制台均可）。所有源文件与编译产物请放在上述目录中，且保证能在目标设备上运行调试。


根据已有的功能， 模块需要集成 nanohttpd 启动本地 HTTP 服务，监听唯一端口（因为在Lsposed里面这个的xposed会被多次启动，导致端口会重复启动），Xposed 模块获取到数据之后，发起请求，并将数据返回给 Python 脚本，编写 Python 脚本。
简单来说：Xposed 模块 HTTP 服务拼多多 App→Xposed 模块→Python 脚本
python脚本就是实时监控的发送的数

账号信息
账号邮箱：
Ravhe_<PERSON><PERSON><PERSON>@linux.do
登录密码：
Y6B2e8CH📋
有效期至：
2025-10-02 10:12:20


https://mobile.pinduoduo.com/goods2.html?_wv=41729&_wvx=10&_x_share_id=Kr0Lu9GyUjPbSnYZhGiJe8Mn&goods_id=779406704702&page_from=401&_oc_trace_mark=199&pxq_secret_key=ZSIGPEFGSPJKGGFRZF3XMADTPP5QX6O6WZEXHAUAF4RBPUEHMLLQ&_oak_share_snapshot_num=96&_oak_share_detail_id=10599246257&_oak_share_time=1756190096&share_oak_rcto=YWLKFNSu6Sxc1t3WJ_NFSCRT6i6oQAMH3QsXNUVOAvSogeptJ7lAFxWj&share_uin=6OAAWPMH5TADRC5ICVSA2JDX4I_GEXDA&refer_share_id=okjq839qfuixtee2j9khsj84vhc1w01h&refer_share_uin=6OAAWPMH5TADRC5ICVSA2JDX4I_GEXDA&refer_share_channel=message